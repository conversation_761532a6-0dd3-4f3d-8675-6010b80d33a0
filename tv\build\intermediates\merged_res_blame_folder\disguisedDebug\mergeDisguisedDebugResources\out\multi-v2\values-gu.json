{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-72:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,266,334,409,477,576,672", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "120,185,261,329,404,472,571,667,746"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6520,6590,6655,6731,6799,6874,6942,7041,7137", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "6585,6650,6726,6794,6869,6937,7036,7132,7211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,489,677,759,839,922,1021,1123,1200,1262,1351,1439,1503,1567,1627,1694,1807,1921,2032,2105,2183,2252,2328,2410,2490,2553,2616,2669,2727,2775,2836,2898,2960,3025,3087,3154,3217,3283,3336,3398,3474,3550,3604", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,112,113,110,72,77,68,75,81,79,62,62,52,57,47,60,61,61,64,61,66,62,65,52,61,75,75,53,66", "endOffsets": "281,484,672,754,834,917,1016,1118,1195,1257,1346,1434,1498,1562,1622,1689,1802,1916,2027,2100,2178,2247,2323,2405,2485,2548,2611,2664,2722,2770,2831,2893,2955,3020,3082,3149,3212,3278,3331,3393,3469,3545,3599,3666"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,539,4581,4663,4743,4826,4925,5027,5104,5166,5255,5343,5407,5471,5531,5598,5711,5825,5936,6009,6087,6156,6232,6314,6394,6457,7216,7269,7327,7375,7436,7498,7560,7625,7687,7754,7817,7883,7936,7998,8074,8150,8204", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,112,113,110,72,77,68,75,81,79,62,62,52,57,47,60,61,61,64,61,66,62,65,52,61,75,75,53,66", "endOffsets": "331,534,722,4658,4738,4821,4920,5022,5099,5161,5250,5338,5402,5466,5526,5593,5706,5820,5931,6004,6082,6151,6227,6309,6389,6452,6515,7264,7322,7370,7431,7493,7555,7620,7682,7749,7812,7878,7931,7993,8069,8145,8199,8266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,1001,1084,1159,1234,1309,1384,1460,1526", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,74,74,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,996,1079,1154,1229,1304,1379,1455,1521,1637"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4128,4220,4302,4395,4494,8271,8357,8458,8545,8631,8714,8878,8953,9028,9103,9279,9355,9421", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,74,74,74,75,65,115", "endOffsets": "4215,4297,4390,4489,4576,8352,8453,8540,8626,8709,8792,8948,9023,9098,9173,9350,9416,9532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3410,3504,3607,3704,3806,3908,4006,9178", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3499,3602,3699,3801,3903,4001,4123,9274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9537,9622", "endColumns": "84,84", "endOffsets": "9617,9702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "727,834,938,1045,1132,1232,1352,1430,1507,1598,1691,1786,1880,1980,2073,2168,2262,2353,2444,2524,2630,2731,2828,2937,3037,3147,3307,8797", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "829,933,1040,1127,1227,1347,1425,1502,1593,1686,1781,1875,1975,2068,2163,2257,2348,2439,2519,2625,2726,2823,2932,3032,3142,3302,3405,8873"}}]}]}