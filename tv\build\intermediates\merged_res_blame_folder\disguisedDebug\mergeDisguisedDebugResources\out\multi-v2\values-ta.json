{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-72:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,148", "endColumns": "92,97", "endOffsets": "143,241"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9991,10084", "endColumns": "92,97", "endOffsets": "10079,10177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3516,3612,3715,3814,3912,4019,4134,9621", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3607,3710,3809,3907,4014,4129,4257,9717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,789,878,959,1064,1168,1247,1313,1409,1506,1577,1642,1704,1776,1923,2066,2215,2284,2368,2441,2521,2623,2725,2792,2860,2913,2976,3024,3085,3152,3217,3278,3347,3410,3473,3539,3593,3657,3735,3813,3869", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,53,63,77,77,55,67", "endOffsets": "280,500,695,784,873,954,1059,1163,1242,1308,1404,1501,1572,1637,1699,1771,1918,2061,2210,2279,2363,2436,2516,2618,2720,2787,2855,2908,2971,3019,3080,3147,3212,3273,3342,3405,3468,3534,3588,3652,3730,3808,3864,3932"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,555,4729,4818,4907,4988,5093,5197,5276,5342,5438,5535,5606,5671,5733,5805,5952,6095,6244,6313,6397,6470,6550,6652,6754,6821,7597,7650,7713,7761,7822,7889,7954,8015,8084,8147,8210,8276,8330,8394,8472,8550,8606", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,53,63,77,77,55,67", "endOffsets": "330,550,745,4813,4902,4983,5088,5192,5271,5337,5433,5530,5601,5666,5728,5800,5947,6090,6239,6308,6392,6465,6545,6647,6749,6816,6884,7645,7708,7756,7817,7884,7949,8010,8079,8142,8205,8271,8325,8389,8467,8545,8601,8669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1032,1118,1208,1293,1366,1437,1517,1586", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1027,1113,1203,1288,1361,1432,1512,1581,1701"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4262,4359,4443,4537,4638,8674,8757,8866,8957,9052,9134,9302,9392,9477,9550,9722,9802,9871", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,84,72,70,79,68,119", "endOffsets": "4354,4438,4532,4633,4724,8752,8861,8952,9047,9129,9215,9387,9472,9545,9616,9797,9866,9986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "750,863,965,1080,1169,1280,1401,1480,1556,1654,1754,1849,1943,2050,2150,2252,2346,2444,2542,2623,2731,2834,2933,3049,3152,3257,3414,9220", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "858,960,1075,1164,1275,1396,1475,1551,1649,1749,1844,1938,2045,2145,2247,2341,2439,2537,2618,2726,2829,2928,3044,3147,3252,3409,3511,9297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6889,6958,7031,7100,7170,7252,7333,7430,7515", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "6953,7026,7095,7165,7247,7328,7425,7510,7592"}}]}]}