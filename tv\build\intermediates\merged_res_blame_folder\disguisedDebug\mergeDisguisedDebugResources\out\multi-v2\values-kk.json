{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-72:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,1004,1089,1162,1236,1312,1386,1462,1532", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,999,1084,1157,1231,1307,1381,1457,1527,1645"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4132,4225,4308,4413,4515,8446,8527,8620,8710,8792,8875,9042,9115,9189,9265,9440,9516,9586", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "4220,4303,4408,4510,4597,8522,8615,8705,8787,8870,8955,9110,9184,9260,9334,9511,9581,9699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "727,835,940,1050,1135,1241,1360,1440,1517,1608,1701,1796,1890,1990,2083,2178,2275,2366,2457,2538,2643,2746,2844,2951,3057,3157,3323,8960", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "830,935,1045,1130,1236,1355,1435,1512,1603,1696,1791,1885,1985,2078,2173,2270,2361,2452,2533,2638,2741,2839,2946,3052,3152,3318,3413,9037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,147", "endColumns": "91,95", "endOffsets": "142,238"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9704,9796", "endColumns": "91,95", "endOffsets": "9791,9887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3418,3513,3615,3717,3820,3924,4021,9339", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3508,3610,3712,3815,3919,4016,4127,9435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,483,677,757,837,924,1021,1118,1203,1268,1364,1461,1528,1593,1659,1729,1862,1994,2125,2201,2277,2351,2437,2526,2615,2681,2747,2800,2860,2908,2969,3029,3096,3161,3226,3289,3346,3418,3470,3531,3613,3695,3750", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,132,131,130,75,75,73,85,88,88,65,65,52,59,47,60,59,66,64,64,62,56,71,51,60,81,81,54,66", "endOffsets": "281,478,672,752,832,919,1016,1113,1198,1263,1359,1456,1523,1588,1654,1724,1857,1989,2120,2196,2272,2346,2432,2521,2610,2676,2742,2795,2855,2903,2964,3024,3091,3156,3221,3284,3341,3413,3465,3526,3608,3690,3745,3812"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,533,4602,4682,4762,4849,4946,5043,5128,5193,5289,5386,5453,5518,5584,5654,5787,5919,6050,6126,6202,6276,6362,6451,6540,6606,7376,7429,7489,7537,7598,7658,7725,7790,7855,7918,7975,8047,8099,8160,8242,8324,8379", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,132,131,130,75,75,73,85,88,88,65,65,52,59,47,60,59,66,64,64,62,56,71,51,60,81,81,54,66", "endOffsets": "331,528,722,4677,4757,4844,4941,5038,5123,5188,5284,5381,5448,5513,5579,5649,5782,5914,6045,6121,6197,6271,6357,6446,6535,6601,6667,7424,7484,7532,7593,7653,7720,7785,7850,7913,7970,8042,8094,8155,8237,8319,8374,8441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,184,252,316,401,488,585,681", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "115,179,247,311,396,483,580,676,754"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6672,6737,6801,6869,6933,7018,7105,7202,7298", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "6732,6796,6864,6928,7013,7100,7197,7293,7371"}}]}]}