{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-72:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1458,1555,1627,1700,1760,1830,1947,2061,2181,2260,2352,2420,2506,2592,2677,2746,2809,2862,2920,2968,3029,3091,3162,3224,3286,3345,3412,3478,3532,3594,3670,3746,3799", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,92,92,89,100,102,85,63,96,96,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,53,61,75,75,52,64", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1453,1550,1622,1695,1755,1825,1942,2056,2176,2255,2347,2415,2501,2587,2672,2741,2804,2857,2915,2963,3024,3086,3157,3219,3281,3340,3407,3473,3527,3589,3665,3741,3794,3859"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,360,579,4734,4827,4920,5010,5111,5214,5300,5364,5461,5558,5630,5703,5763,5833,5950,6064,6184,6263,6355,6423,6509,6595,6680,6749,7509,7562,7620,7668,7729,7791,7862,7924,7986,8045,8112,8178,8232,8294,8370,8446,8499", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "17,12,12,92,92,89,100,102,85,63,96,96,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,53,61,75,75,52,64", "endOffsets": "355,574,776,4822,4915,5005,5106,5209,5295,5359,5456,5553,5625,5698,5758,5828,5945,6059,6179,6258,6350,6418,6504,6590,6675,6744,6807,7557,7615,7663,7724,7786,7857,7919,7981,8040,8107,8173,8227,8289,8365,8441,8494,8559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "46,47,48,49,50,51,52,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3548,3651,3754,3856,3962,4060,4160,9475", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3646,3749,3851,3957,4055,4155,4263,9571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6812,6883,6954,7024,7091,7169,7246,7346,7440", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "6878,6949,7019,7086,7164,7241,7341,7435,7504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,890,1001,1109,1200,1307,1427,1511,1590,1681,1774,1869,1963,2063,2156,2251,2345,2436,2527,2613,2726,2827,2930,3043,3153,3270,3437,9098", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "885,996,1104,1195,1302,1422,1506,1585,1676,1769,1864,1958,2058,2151,2246,2340,2431,2522,2608,2721,2822,2925,3038,3148,3265,3432,3543,9173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,1012,1105,1182,1257,1330,1402,1483,1551", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,74,72,71,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,1007,1100,1177,1252,1325,1397,1478,1546,1666"}, "to": {"startLines": "53,54,55,56,57,108,109,110,111,112,113,115,116,117,118,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4268,4367,4457,4551,4648,8564,8646,8742,8829,8915,9005,9178,9255,9330,9403,9576,9657,9725", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,74,72,71,80,67,119", "endOffsets": "4362,4452,4546,4643,4729,8641,8737,8824,8910,9000,9093,9250,9325,9398,9470,9652,9720,9840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,90", "endOffsets": "135,226"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "9845,9930", "endColumns": "84,90", "endOffsets": "9925,10016"}}]}]}