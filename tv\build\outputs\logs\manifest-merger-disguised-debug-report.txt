-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:67:9-75:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:71:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:69:13-64
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:70:13-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:68:13-62
manifest
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:2:1-83:12
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\1de9c3367d22c2c2defaf55b8bc75b83\transformed\ijkplayer-cmake-release\AndroidManifest.xml:2:1-7:12
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\1e756b276c5f4d3d6217ddc2fdd21461\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:2:1-13:12
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-android\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-android\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-android\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb3580c89e0f754739c5e33a7775062f\transformed\coil-svg-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b3e8d8f22d9b1d24e21228f69202e0b\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c7a5259abcf4591a1af4918912c4a95\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\98f6be0ca302af34f211a23231a198f1\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d3998365c79e1c032d449e3242957ec\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\018c63b1485334add67611329c38a7ad\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c98e1a41ddbd0f58bbf4b7505ad9616c\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5a79ec61513eb95e1266e907126a2152\transformed\media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a37876d194a1c01504b2fb986c266ebc\transformed\media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53fc7b51d5b2697341384011a3c0471e\transformed\media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6639b55e37bc2c6c72b9eb1a2e0e5f0\transformed\media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9bd9b1c7db019bbf9d5e9e5138559418\transformed\media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49e3b02bf70dfb50c7d45b6b2fe0c09\transformed\media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c1bb1cbb4c0c4dd816b59ba365baac29\transformed\media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8fc5456a656e16c6eff439804e76877d\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9772acafa61ad9e4da3e225fb0bb1b75\transformed\media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c593f451a3b6a484d9ae1bd19252742\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9db5af2f2458547656275ea3d5385dab\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1e116a809e646e532a718de75b1ceef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tv:tv-material:1.0.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\731aaaf9c3f1ab4a2dedba4b7a3d0ca3\transformed\tv-material-1.0.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\e27b26ab4fdbef454305ada43ffa6aaf\transformed\navigation-common-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\00b00a57a1321b3abddaddf7bed980a7\transformed\navigation-runtime-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\901134bfa3027fdc4efc5e9b1dbbf70b\transformed\navigation-common-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\a78bc4bbe8c8b7fc76a9ffad5cc4261e\transformed\navigation-runtime-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb7529dec08a16f0ba21b419b9e507fa\transformed\navigation-compose-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\e3284905063e776d62ae70595f5b34b4\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae30512adfd89f17d52d28ae78730ad7\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c124e34a7f4172b432f9fe272bec823\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e7e1990e3a2927aa964ef667f1a3836\transformed\activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4529266a8c3263663ff3251489430cac\transformed\sentry-android-7.13.0\AndroidManifest.xml:2:1-10:12
MERGED from [io.sentry:sentry-android-ndk:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f28bad37832eee3b2b77e0536190b563\transformed\sentry-android-ndk-7.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:2:1-23:12
MERGED from [androidx.compose.material:material-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e8e953c8ca6396d05eae233b004a84\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b73b969236973d78657ede4f0f194d2a\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\db4a6c88d4347ce178fe3684539ea8e0\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab891530b64eecdf0e7491d248519c0a\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2510da7f39df9715e457e0526689281c\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\790bca944b41d25834ca5cde0d5c6974\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d8fa77734273d7df92383485cc746c5\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f33fb5b142e37b2e11aba2dc17a6b8a\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2beb69d6875208f2d04111a35001bba9\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\876bb1e606565bc27f304f01793afc9b\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e9d2cb82d2fe484f7dcd235e8334edb\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30ac6839eb42bcb86357a0d144ffee2c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d6da967e4281651358e6dd3dac6ee93\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a24962cf41916db9572a74a797a0e06\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\06f54bc868185307d2008338de4b6f8a\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\63ccdacec2c798c3da3c085cba28b3e6\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bdcbc52a7f4fc574f82c6a94b68ef448\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7360db8db21da0cd2a2513de2c76e03\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f73895031d02ca8d1d64315fa193d91\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a6c0d6aba12adda640fe295311b9cada\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-compose-android:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7eaf4be4378c6421cd046bcb49eb119\transformed\sentry-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\afa94a096a9eeb83f65f48490bf80e41\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3e7b3daac682e3549d3763424bb713a\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d35c56726b65064adaa3dd1f91e7d98\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ac6608a66c236e16140b2df2b55713a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba6757b190126e7429206a459baebc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dbb727f01cc86fe24c49a72666e9e7f\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe448b3d7d63514c1e70b7a8388f22f\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1567b0fbf55afdc467b005b9468367d5\transformed\qrose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\71c906e64b4038d485f5392a4163e37c\transformed\qrose-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\762595ecebbae88b00b6c235b65822da\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0ce41bc1ab5a7ccfdd795c249b2c5b2\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fde23f3bbc10ce5c49e062b340e080e0\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.animation:animation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8b8e32440ebe6b1a27aee4fb611b8ab\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a99e45a91607e1c71c206aab1452b5b3\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd2de9df1fcb7d47d0f664953b17c729\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a112f10a8620d44c07292457d5e069f0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bd501f6f99f84825091a2bbfc5614134\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\effa78477564d01358468880b73f82a7\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f40c40fb42cdb0ba22401bf8fc1df14\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f1fecd21f0cb9107e9c3751c3fdde6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8341b782ba39e57543abacf09495b30f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1e35c9300b09aae36d857ae78ea9a9a\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bc5956aa20aa1b6e506265a26f6d087\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f73974b6797712fe76a28e8dd126afcc\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e8e26f56d8e20e616f6e0fb0878dba9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\db0874ac1ef75b4773055a57b3321661\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aaf0057ac6b1dde88a2094d678e675b3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android-replay:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9e4d488c095bf9ecf0df049413f40d8\transformed\sentry-android-replay-7.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d285fd8f859c007aafe8f8b92f5c8b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4545091883f438d426e29f97df13794d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe79de89205f90529a69370bf3cb9e6c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b370e2a3fcf58d4b2bff301247fd3a3\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\02067b2103c072e242f8edf396ec5f20\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16fd53a0a059ccaadae1782f4d1de9c3\transformed\androidasync-3.1.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3a07bceaa23ef51ac9866ac1768a7c38\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
MERGED from [io.sentry:sentry-android-fragment:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\edc10a53af33a9c2d49a3f0a29adf89c\transformed\sentry-android-fragment-7.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android-navigation:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f3878d40f2d4d043756eef443dba62d\transformed\sentry-android-navigation-7.13.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.touchscreen
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:5:5-7:36
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:7:9-33
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:6:9-52
uses-feature#android.software.leanback
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:8:5-10:36
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:10:9-33
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:9:9-49
queries
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:12:5-14:15
package#com.google.android.webview
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:13:9-62
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:13:18-59
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:16:5-67
MERGED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:7:5-67
MERGED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:7:5-67
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16fd53a0a059ccaadae1782f4d1de9c3\transformed\androidasync-3.1.0\AndroidManifest.xml:11:5-67
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16fd53a0a059ccaadae1782f4d1de9c3\transformed\androidasync-3.1.0\AndroidManifest.xml:11:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:17:5-81
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:18:5-83
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:18:22-80
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:19:5-80
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:19:22-77
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:20:5-22:40
	tools:ignore
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:22:9-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:21:9-66
application
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:24:5-81:19
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:24:5-81:19
MERGED from [io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:7:5-11:19
MERGED from [io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:7:5-11:19
MERGED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:9:5-21:19
MERGED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:9:5-21:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16fd53a0a059ccaadae1782f4d1de9c3\transformed\androidasync-3.1.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16fd53a0a059ccaadae1782f4d1de9c3\transformed\androidasync-3.1.0\AndroidManifest.xml:13:5-14:19
	package
		ADDED from [io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:8:9-29
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:31:9-52
	android:versionCode
		ADDED from [io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:9:9-32
	android:icon
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:28:9-43
	android:banner
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:27:9-43
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:30:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:32:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:29:9-41
	android:versionName
		ADDED from [io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:10:9-34
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:35:9-28
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:26:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:33:9-42
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:34:9-44
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:25:9-40
activity#top.yogiczy.mytv.tv.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:36:9-50:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:41:13-56
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:39:13-36
	android:supportsPictureInPicture
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:42:13-52
	android:resizeableActivity
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:40:13-46
	tools:ignore
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:43:13-42
	android:configChanges
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:38:13-119
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:37:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:44:13-49:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:45:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:45:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:47:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:47:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:48:17-86
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:48:27-83
activity#top.yogiczy.mytv.tv.CrashHandlerActivity
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:52:9-58
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:52:19-55
receiver#top.yogiczy.mytv.tv.BootReceiver
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:54:9-63:20
	android:enabled
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:56:13-35
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:57:13-37
	android:permission
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:58:13-75
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:55:13-41
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:59:13-62:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:60:17-79
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:60:25-76
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:61:17-76
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:61:27-73
service#top.yogiczy.mytv.tv.HttpServerService
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:65:9-54
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:65:18-51
meta-data#io.sentry.auto-init
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:77:9-79:37
	android:value
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:79:13-34
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:78:13-47
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:72:13-74:54
	android:resource
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:74:17-51
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:73:17-67
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\1de9c3367d22c2c2defaf55b8bc75b83\transformed\ijkplayer-cmake-release\AndroidManifest.xml:5:5-44
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\1de9c3367d22c2c2defaf55b8bc75b83\transformed\ijkplayer-cmake-release\AndroidManifest.xml:5:5-44
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\1e756b276c5f4d3d6217ddc2fdd21461\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:20:5-44
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\1e756b276c5f4d3d6217ddc2fdd21461\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:20:5-44
MERGED from [io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:5:5-44
MERGED from [io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:5:5-44
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-android\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-android\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-android\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-android\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-android\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-android\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb3580c89e0f754739c5e33a7775062f\transformed\coil-svg-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb3580c89e0f754739c5e33a7775062f\transformed\coil-svg-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b3e8d8f22d9b1d24e21228f69202e0b\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b3e8d8f22d9b1d24e21228f69202e0b\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c7a5259abcf4591a1af4918912c4a95\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c7a5259abcf4591a1af4918912c4a95\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\98f6be0ca302af34f211a23231a198f1\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\98f6be0ca302af34f211a23231a198f1\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d3998365c79e1c032d449e3242957ec\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d3998365c79e1c032d449e3242957ec\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\018c63b1485334add67611329c38a7ad\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\018c63b1485334add67611329c38a7ad\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c98e1a41ddbd0f58bbf4b7505ad9616c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c98e1a41ddbd0f58bbf4b7505ad9616c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5a79ec61513eb95e1266e907126a2152\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5a79ec61513eb95e1266e907126a2152\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a37876d194a1c01504b2fb986c266ebc\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a37876d194a1c01504b2fb986c266ebc\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53fc7b51d5b2697341384011a3c0471e\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\53fc7b51d5b2697341384011a3c0471e\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6639b55e37bc2c6c72b9eb1a2e0e5f0\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6639b55e37bc2c6c72b9eb1a2e0e5f0\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9bd9b1c7db019bbf9d5e9e5138559418\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9bd9b1c7db019bbf9d5e9e5138559418\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49e3b02bf70dfb50c7d45b6b2fe0c09\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b49e3b02bf70dfb50c7d45b6b2fe0c09\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c1bb1cbb4c0c4dd816b59ba365baac29\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c1bb1cbb4c0c4dd816b59ba365baac29\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8fc5456a656e16c6eff439804e76877d\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8fc5456a656e16c6eff439804e76877d\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9772acafa61ad9e4da3e225fb0bb1b75\transformed\media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9772acafa61ad9e4da3e225fb0bb1b75\transformed\media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c593f451a3b6a484d9ae1bd19252742\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c593f451a3b6a484d9ae1bd19252742\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9db5af2f2458547656275ea3d5385dab\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9db5af2f2458547656275ea3d5385dab\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1e116a809e646e532a718de75b1ceef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1e116a809e646e532a718de75b1ceef\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tv:tv-material:1.0.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\731aaaf9c3f1ab4a2dedba4b7a3d0ca3\transformed\tv-material-1.0.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-material:1.0.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\731aaaf9c3f1ab4a2dedba4b7a3d0ca3\transformed\tv-material-1.0.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\e27b26ab4fdbef454305ada43ffa6aaf\transformed\navigation-common-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\e27b26ab4fdbef454305ada43ffa6aaf\transformed\navigation-common-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\00b00a57a1321b3abddaddf7bed980a7\transformed\navigation-runtime-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\00b00a57a1321b3abddaddf7bed980a7\transformed\navigation-runtime-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\901134bfa3027fdc4efc5e9b1dbbf70b\transformed\navigation-common-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\901134bfa3027fdc4efc5e9b1dbbf70b\transformed\navigation-common-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\a78bc4bbe8c8b7fc76a9ffad5cc4261e\transformed\navigation-runtime-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\a78bc4bbe8c8b7fc76a9ffad5cc4261e\transformed\navigation-runtime-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb7529dec08a16f0ba21b419b9e507fa\transformed\navigation-compose-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\eb7529dec08a16f0ba21b419b9e507fa\transformed\navigation-compose-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\e3284905063e776d62ae70595f5b34b4\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\e3284905063e776d62ae70595f5b34b4\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae30512adfd89f17d52d28ae78730ad7\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\ae30512adfd89f17d52d28ae78730ad7\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c124e34a7f4172b432f9fe272bec823\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c124e34a7f4172b432f9fe272bec823\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e7e1990e3a2927aa964ef667f1a3836\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e7e1990e3a2927aa964ef667f1a3836\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4529266a8c3263663ff3251489430cac\transformed\sentry-android-7.13.0\AndroidManifest.xml:6:5-8:57
MERGED from [io.sentry:sentry-android:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4529266a8c3263663ff3251489430cac\transformed\sentry-android-7.13.0\AndroidManifest.xml:6:5-8:57
MERGED from [io.sentry:sentry-android-ndk:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f28bad37832eee3b2b77e0536190b563\transformed\sentry-android-ndk-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-ndk:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f28bad37832eee3b2b77e0536190b563\transformed\sentry-android-ndk-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e8e953c8ca6396d05eae233b004a84\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e8e953c8ca6396d05eae233b004a84\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b73b969236973d78657ede4f0f194d2a\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b73b969236973d78657ede4f0f194d2a\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\db4a6c88d4347ce178fe3684539ea8e0\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\db4a6c88d4347ce178fe3684539ea8e0\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab891530b64eecdf0e7491d248519c0a\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab891530b64eecdf0e7491d248519c0a\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2510da7f39df9715e457e0526689281c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2510da7f39df9715e457e0526689281c\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\790bca944b41d25834ca5cde0d5c6974\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\790bca944b41d25834ca5cde0d5c6974\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d8fa77734273d7df92383485cc746c5\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d8fa77734273d7df92383485cc746c5\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f33fb5b142e37b2e11aba2dc17a6b8a\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f33fb5b142e37b2e11aba2dc17a6b8a\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2beb69d6875208f2d04111a35001bba9\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\2beb69d6875208f2d04111a35001bba9\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\876bb1e606565bc27f304f01793afc9b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\876bb1e606565bc27f304f01793afc9b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e9d2cb82d2fe484f7dcd235e8334edb\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e9d2cb82d2fe484f7dcd235e8334edb\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30ac6839eb42bcb86357a0d144ffee2c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30ac6839eb42bcb86357a0d144ffee2c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d6da967e4281651358e6dd3dac6ee93\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d6da967e4281651358e6dd3dac6ee93\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a24962cf41916db9572a74a797a0e06\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1a24962cf41916db9572a74a797a0e06\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\06f54bc868185307d2008338de4b6f8a\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\06f54bc868185307d2008338de4b6f8a\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\63ccdacec2c798c3da3c085cba28b3e6\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\63ccdacec2c798c3da3c085cba28b3e6\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bdcbc52a7f4fc574f82c6a94b68ef448\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bdcbc52a7f4fc574f82c6a94b68ef448\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7360db8db21da0cd2a2513de2c76e03\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7360db8db21da0cd2a2513de2c76e03\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f73895031d02ca8d1d64315fa193d91\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f73895031d02ca8d1d64315fa193d91\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a6c0d6aba12adda640fe295311b9cada\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a6c0d6aba12adda640fe295311b9cada\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-compose-android:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7eaf4be4378c6421cd046bcb49eb119\transformed\sentry-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-compose-android:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d7eaf4be4378c6421cd046bcb49eb119\transformed\sentry-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\afa94a096a9eeb83f65f48490bf80e41\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\afa94a096a9eeb83f65f48490bf80e41\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3e7b3daac682e3549d3763424bb713a\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\d3e7b3daac682e3549d3763424bb713a\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d35c56726b65064adaa3dd1f91e7d98\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d35c56726b65064adaa3dd1f91e7d98\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ac6608a66c236e16140b2df2b55713a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4ac6608a66c236e16140b2df2b55713a\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba6757b190126e7429206a459baebc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bba6757b190126e7429206a459baebc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dbb727f01cc86fe24c49a72666e9e7f\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0dbb727f01cc86fe24c49a72666e9e7f\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe448b3d7d63514c1e70b7a8388f22f\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fe448b3d7d63514c1e70b7a8388f22f\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1567b0fbf55afdc467b005b9468367d5\transformed\qrose-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1567b0fbf55afdc467b005b9468367d5\transformed\qrose-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\71c906e64b4038d485f5392a4163e37c\transformed\qrose-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\71c906e64b4038d485f5392a4163e37c\transformed\qrose-core-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\762595ecebbae88b00b6c235b65822da\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\762595ecebbae88b00b6c235b65822da\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0ce41bc1ab5a7ccfdd795c249b2c5b2\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\f0ce41bc1ab5a7ccfdd795c249b2c5b2\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fde23f3bbc10ce5c49e062b340e080e0\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fde23f3bbc10ce5c49e062b340e080e0\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8b8e32440ebe6b1a27aee4fb611b8ab\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8b8e32440ebe6b1a27aee4fb611b8ab\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a99e45a91607e1c71c206aab1452b5b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\a99e45a91607e1c71c206aab1452b5b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd2de9df1fcb7d47d0f664953b17c729\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\fd2de9df1fcb7d47d0f664953b17c729\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a112f10a8620d44c07292457d5e069f0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a112f10a8620d44c07292457d5e069f0\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bd501f6f99f84825091a2bbfc5614134\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bd501f6f99f84825091a2bbfc5614134\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\effa78477564d01358468880b73f82a7\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\effa78477564d01358468880b73f82a7\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f40c40fb42cdb0ba22401bf8fc1df14\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f40c40fb42cdb0ba22401bf8fc1df14\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f1fecd21f0cb9107e9c3751c3fdde6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f1fecd21f0cb9107e9c3751c3fdde6b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8341b782ba39e57543abacf09495b30f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8341b782ba39e57543abacf09495b30f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1e35c9300b09aae36d857ae78ea9a9a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1e35c9300b09aae36d857ae78ea9a9a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bc5956aa20aa1b6e506265a26f6d087\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bc5956aa20aa1b6e506265a26f6d087\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f73974b6797712fe76a28e8dd126afcc\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f73974b6797712fe76a28e8dd126afcc\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e8e26f56d8e20e616f6e0fb0878dba9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e8e26f56d8e20e616f6e0fb0878dba9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\db0874ac1ef75b4773055a57b3321661\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\db0874ac1ef75b4773055a57b3321661\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aaf0057ac6b1dde88a2094d678e675b3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aaf0057ac6b1dde88a2094d678e675b3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-replay:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9e4d488c095bf9ecf0df049413f40d8\transformed\sentry-android-replay-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-replay:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b9e4d488c095bf9ecf0df049413f40d8\transformed\sentry-android-replay-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8021988f1600a50d6929200a80486d92\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d285fd8f859c007aafe8f8b92f5c8b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d285fd8f859c007aafe8f8b92f5c8b9b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4545091883f438d426e29f97df13794d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4545091883f438d426e29f97df13794d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe79de89205f90529a69370bf3cb9e6c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe79de89205f90529a69370bf3cb9e6c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b370e2a3fcf58d4b2bff301247fd3a3\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b370e2a3fcf58d4b2bff301247fd3a3\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\02067b2103c072e242f8edf396ec5f20\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\02067b2103c072e242f8edf396ec5f20\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16fd53a0a059ccaadae1782f4d1de9c3\transformed\androidasync-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16fd53a0a059ccaadae1782f4d1de9c3\transformed\androidasync-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3a07bceaa23ef51ac9866ac1768a7c38\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\3a07bceaa23ef51ac9866ac1768a7c38\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [io.sentry:sentry-android-fragment:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\edc10a53af33a9c2d49a3f0a29adf89c\transformed\sentry-android-fragment-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-fragment:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\edc10a53af33a9c2d49a3f0a29adf89c\transformed\sentry-android-fragment-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-navigation:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f3878d40f2d4d043756eef443dba62d\transformed\sentry-android-navigation-7.13.0\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-navigation:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f3878d40f2d4d043756eef443dba62d\transformed\sentry-android-navigation-7.13.0\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [io.sentry:sentry-android:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4529266a8c3263663ff3251489430cac\transformed\sentry-android-7.13.0\AndroidManifest.xml:8:9-54
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c1bb1cbb4c0c4dd816b59ba365baac29\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9db5af2f2458547656275ea3d5385dab\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9db5af2f2458547656275ea3d5385dab\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c1bb1cbb4c0c4dd816b59ba365baac29\transformed\media3-common-1.4.1\AndroidManifest.xml:22:22-76
provider#io.sentry.android.core.SentryInitProvider
ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:12:9-15:40
	android:authorities
		ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:14:13-70
	android:exported
		ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:15:13-37
	android:name
		ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:13:13-69
provider#io.sentry.android.core.SentryPerformanceProvider
ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:16:9-20:39
	android:authorities
		ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:18:13-77
	android:exported
		ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:19:13-37
	android:initOrder
		ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:20:13-36
	android:name
		ADDED from [io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:17:13-76
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\02a2dc3fb6f2f628d8f50a1ef0801c78\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
