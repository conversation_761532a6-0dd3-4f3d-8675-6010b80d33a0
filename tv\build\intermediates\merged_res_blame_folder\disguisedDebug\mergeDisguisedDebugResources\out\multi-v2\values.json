{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeDisguisedDebugResources-72:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "29,33,40,98,104,105,106,107,108,109,110,111,112,115,116,117,118,119,120,121,122,123,124,125,126,129,130,131,132,133,134,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,269,270,309,310,311,312,313,314,315,334,335,336,337,338,339,340,341,423,424,425,426,477,486,487,490,507,514,515,516,517,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,628,639,640,641,642,643,644,652,653,657,661,665,670,676,683,687,691,696,700,704,708,712,716,720,726,730,736,740,746,750,755,759,762,766,772,776,782,786,792,795,799,803,807,811,815,816,817,818,821,824,827,830,834,835,836,837,838,841,843,845,847,852,853,857,863,867,868,870,882,883,887,893,897,898,899,903,930,934,935,939,967,1139,1165,1336,1362,1393,1401,1407,1423,1445,1450,1455,1465,1474,1483,1487,1494,1513,1520,1521,1530,1533,1536,1540,1544,1548,1551,1552,1557,1562,1572,1577,1584,1590,1591,1594,1598,1603,1605,1607,1610,1613,1615,1619,1622,1629,1632,1635,1639,1641,1645,1647,1649,1651,1655,1663,1671,1683,1689,1698,1701,1712,1715,1716,1721,1722,1903,1972,2042,2043,2053,2062,2063,2065,2069,2072,2075,2078,2081,2084,2087,2090,2094,2097,2100,2103,2107,2110,2114,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2140,2142,2143,2144,2145,2146,2147,2148,2149,2151,2152,2154,2155,2157,2159,2160,2162,2163,2164,2165,2166,2167,2169,2170,2171,2172,2173,2190,2192,2194,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2210,2211,2212,2213,2214,2215,2216,2218,2222,2227,2228,2229,2230,2231,2232,2236,2237,2238,2239,2241,2243,2245,2247,2249,2250,2251,2252,2254,2256,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2272,2273,2274,2275,2277,2279,2280,2282,2283,2285,2287,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2302,2303,2304,2305,2307,2308,2309,2310,2311,2313,2315,2317,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2336,2411,2414,2417,2420,2434,2447,2489,2492,2521,2548,2557,2621,2987,2997,3035,3079,3225,3249,3255,3261,3282,3406,3567,3573,3577,3605,3640,3672,3738,3758,3813,3825,3851", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1133,1362,1637,4346,4639,4694,4756,4820,4890,4951,5026,5102,5179,5417,5502,5584,5660,5736,5813,5891,5997,6103,6182,6262,6319,6508,6582,6657,6722,6788,6848,7325,7397,7470,7537,7605,7664,7723,7782,7841,7900,7954,8008,8061,8115,8169,8223,8498,8572,8651,8724,8798,8869,8941,9013,9086,9143,9201,9274,9348,9422,9497,9569,9642,9712,9783,9843,9904,9973,10042,10112,10186,10262,10326,10403,10479,10556,10621,10690,10767,10842,10911,10979,11056,11122,11183,11280,11345,11414,11513,11584,11643,11701,11758,11817,11881,11952,12024,12096,12168,12240,12307,12375,12443,12502,12565,12629,12719,12810,12870,12936,13003,13069,13139,13203,13256,13323,13384,13451,13564,13622,13685,13750,13815,13890,13963,14035,14079,14126,14172,14221,14282,14343,14404,14466,14530,14594,14658,14723,14786,14846,14907,14973,15032,15092,15154,15225,15285,15841,15927,18256,18346,18433,18521,18603,18686,18776,20068,20120,20178,20223,20289,20353,20410,20467,26776,26833,26881,26930,29310,29771,29818,29974,30879,31235,31299,31361,31421,32271,32345,32415,32493,32547,32617,32702,32750,32796,32857,32920,32986,33050,33121,33184,33249,33313,33374,33435,33487,33560,33634,33703,33778,33852,33926,34067,39536,40118,40196,40286,40374,40470,40560,41142,41231,41478,41759,42011,42296,42689,43166,43388,43610,43886,44113,44343,44573,44803,45033,45260,45679,45905,46330,46560,46988,47207,47490,47698,47829,48056,48482,48707,49134,49355,49780,49900,50176,50477,50801,51092,51406,51543,51674,51779,52021,52188,52392,52600,52871,52983,53095,53200,53317,53531,53677,53817,53903,54251,54339,54585,55003,55252,55334,55432,56089,56189,56441,56865,57120,57214,57303,57540,59564,59806,59908,60161,62317,72998,74514,85209,86737,88494,89120,89540,90801,92066,92322,92558,93105,93599,94204,94402,94982,96350,96725,96843,97381,97538,97734,98007,98263,98433,98574,98638,99003,99370,100046,100310,100648,101001,101095,101281,101587,101849,101974,102101,102340,102551,102670,102863,103040,103495,103676,103798,104057,104170,104357,104459,104566,104695,104970,105478,105974,106851,107145,107715,107864,108596,108768,108852,109188,109280,119388,124619,129990,130052,130630,131214,131305,131418,131647,131807,131959,132130,132296,132465,132632,132795,133038,133208,133381,133552,133826,134025,134230,134560,134644,134740,134836,134934,135034,135136,135238,135340,135442,135544,135644,135740,135852,135981,136104,136235,136366,136464,136578,136672,136812,136946,137042,137154,137254,137370,137466,137578,137678,137818,137954,138118,138248,138406,138556,138697,138841,138976,139088,139238,139366,139494,139630,139762,139892,140022,140134,141414,141560,141704,141842,141908,141998,142074,142178,142268,142370,142478,142586,142686,142766,142858,142956,143066,143118,143196,143302,143394,143498,143608,143730,143893,144132,144212,144312,144402,144512,144602,144843,144937,145043,145135,145235,145347,145461,145577,145693,145787,145901,146013,146115,146235,146357,146439,146543,146663,146789,146887,146981,147069,147181,147297,147419,147531,147706,147822,147908,148000,148112,148236,148303,148429,148497,148625,148769,148897,148966,149061,149176,149289,149388,149497,149608,149719,149820,149925,150025,150155,150246,150369,150463,150575,150661,150765,150861,150949,151067,151171,151275,151401,151489,151597,151697,151787,151897,151981,152083,152167,152221,152285,152391,152477,152587,152671,152930,155546,155664,155779,155859,156220,156757,158161,158239,159583,160944,161332,164175,174336,174674,176345,178278,183375,184126,184388,184588,184967,189245,195062,195291,195442,196639,197722,198572,201598,202342,204473,204813,206124", "endLines": "29,33,40,98,104,105,106,107,108,109,110,111,112,115,116,117,118,119,120,121,122,123,124,125,126,129,130,131,132,133,134,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,269,270,309,310,311,312,313,314,315,334,335,336,337,338,339,340,341,423,424,425,426,477,486,487,490,507,514,515,516,517,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,628,639,640,641,642,643,651,652,656,660,664,669,675,682,686,690,695,699,703,707,711,715,719,725,729,735,739,745,749,754,758,761,765,771,775,781,785,791,794,798,802,806,810,814,815,816,817,820,823,826,829,833,834,835,836,837,840,842,844,846,851,852,856,862,866,867,869,881,882,886,892,896,897,898,902,929,933,934,938,966,1138,1164,1335,1361,1392,1400,1406,1422,1444,1449,1454,1464,1473,1482,1486,1493,1512,1519,1520,1529,1532,1535,1539,1543,1547,1550,1551,1556,1561,1571,1576,1583,1589,1590,1593,1597,1602,1604,1606,1609,1612,1614,1618,1621,1628,1631,1634,1638,1640,1644,1646,1648,1650,1654,1662,1670,1682,1688,1697,1700,1711,1714,1715,1720,1721,1726,1971,2041,2042,2052,2061,2062,2064,2068,2071,2074,2077,2080,2083,2086,2089,2093,2096,2099,2102,2106,2109,2113,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2139,2141,2142,2143,2144,2145,2146,2147,2148,2150,2151,2153,2154,2156,2158,2159,2161,2162,2163,2164,2165,2166,2168,2169,2170,2171,2172,2173,2191,2193,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2209,2210,2211,2212,2213,2214,2215,2217,2221,2225,2227,2228,2229,2230,2231,2235,2236,2237,2238,2240,2242,2244,2246,2248,2249,2250,2251,2253,2255,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2271,2272,2273,2274,2276,2278,2279,2281,2282,2284,2286,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2301,2302,2303,2304,2306,2307,2308,2309,2310,2312,2314,2316,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2410,2413,2416,2419,2433,2439,2456,2491,2520,2547,2556,2620,2983,2990,3024,3062,3096,3248,3254,3260,3281,3405,3425,3572,3576,3582,3639,3651,3737,3757,3812,3824,3850,3857", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1183,1402,1681,4382,4689,4751,4815,4885,4946,5021,5097,5174,5252,5497,5579,5655,5731,5808,5886,5992,6098,6177,6257,6314,6372,6577,6652,6717,6783,6843,6904,7392,7465,7532,7600,7659,7718,7777,7836,7895,7949,8003,8056,8110,8164,8218,8272,8567,8646,8719,8793,8864,8936,9008,9081,9138,9196,9269,9343,9417,9492,9564,9637,9707,9778,9838,9899,9968,10037,10107,10181,10257,10321,10398,10474,10551,10616,10685,10762,10837,10906,10974,11051,11117,11178,11275,11340,11409,11508,11579,11638,11696,11753,11812,11876,11947,12019,12091,12163,12235,12302,12370,12438,12497,12560,12624,12714,12805,12865,12931,12998,13064,13134,13198,13251,13318,13379,13446,13559,13617,13680,13745,13810,13885,13958,14030,14074,14121,14167,14216,14277,14338,14399,14461,14525,14589,14653,14718,14781,14841,14902,14968,15027,15087,15149,15220,15280,15348,15922,16009,18341,18428,18516,18598,18681,18771,18862,20115,20173,20218,20284,20348,20405,20462,20516,26828,26876,26925,26976,29339,29813,29862,30015,30906,31294,31356,31416,31473,32340,32410,32488,32542,32612,32697,32745,32791,32852,32915,32981,33045,33116,33179,33244,33308,33369,33430,33482,33555,33629,33698,33773,33847,33921,34062,34132,39584,40191,40281,40369,40465,40555,41137,41226,41473,41754,42006,42291,42684,43161,43383,43605,43881,44108,44338,44568,44798,45028,45255,45674,45900,46325,46555,46983,47202,47485,47693,47824,48051,48477,48702,49129,49350,49775,49895,50171,50472,50796,51087,51401,51538,51669,51774,52016,52183,52387,52595,52866,52978,53090,53195,53312,53526,53672,53812,53898,54246,54334,54580,54998,55247,55329,55427,56084,56184,56436,56860,57115,57209,57298,57535,59559,59801,59903,60156,62312,72993,74509,85204,86732,88489,89115,89535,90796,92061,92317,92553,93100,93594,94199,94397,94977,96345,96720,96838,97376,97533,97729,98002,98258,98428,98569,98633,98998,99365,100041,100305,100643,100996,101090,101276,101582,101844,101969,102096,102335,102546,102665,102858,103035,103490,103671,103793,104052,104165,104352,104454,104561,104690,104965,105473,105969,106846,107140,107710,107859,108591,108763,108847,109183,109275,109553,124614,129985,130047,130625,131209,131300,131413,131642,131802,131954,132125,132291,132460,132627,132790,133033,133203,133376,133547,133821,134020,134225,134555,134639,134735,134831,134929,135029,135131,135233,135335,135437,135539,135639,135735,135847,135976,136099,136230,136361,136459,136573,136667,136807,136941,137037,137149,137249,137365,137461,137573,137673,137813,137949,138113,138243,138401,138551,138692,138836,138971,139083,139233,139361,139489,139625,139757,139887,140017,140129,140269,141555,141699,141837,141903,141993,142069,142173,142263,142365,142473,142581,142681,142761,142853,142951,143061,143113,143191,143297,143389,143493,143603,143725,143888,144045,144207,144307,144397,144507,144597,144838,144932,145038,145130,145230,145342,145456,145572,145688,145782,145896,146008,146110,146230,146352,146434,146538,146658,146784,146882,146976,147064,147176,147292,147414,147526,147701,147817,147903,147995,148107,148231,148298,148424,148492,148620,148764,148892,148961,149056,149171,149284,149383,149492,149603,149714,149815,149920,150020,150150,150241,150364,150458,150570,150656,150760,150856,150944,151062,151166,151270,151396,151484,151592,151692,151782,151892,151976,152078,152162,152216,152280,152386,152472,152582,152666,152786,155541,155659,155774,155854,156215,156448,157269,158234,159578,160939,161327,164170,174223,174466,176039,177697,178845,184121,184383,184583,184962,189240,189846,195286,195437,195652,197717,198029,201593,202337,204468,204808,206119,206322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c7360db8db21da0cd2a2513de2c76e03\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "508", "startColumns": "4", "startOffsets": "30911", "endColumns": "42", "endOffsets": "30949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e27b26ab4fdbef454305ada43ffa6aaf\\transformed\\navigation-common-2.8.3\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3426,3439,3445,3451,3460", "startColumns": "4,4,4,4,4", "startOffsets": "189851,190490,190734,190981,191344", "endLines": "3438,3444,3450,3453,3464", "endColumns": "24,24,24,24,24", "endOffsets": "190485,190729,190976,191109,191521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4c124e34a7f4172b432f9fe272bec823\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "488,509", "startColumns": "4,4", "startOffsets": "29867,30954", "endColumns": "41,59", "endOffsets": "29904,31009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\018c63b1485334add67611329c38a7ad\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2457,2473,2479,3652,3668", "startColumns": "4,4,4,4,4", "startOffsets": "157274,157699,157877,198034,198445", "endLines": "2472,2478,2488,3667,3671", "endColumns": "24,24,24,24,24", "endOffsets": "157694,157872,158156,198440,198567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "42,113,114,127,128,158,159,262,263,264,265,266,267,268,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,388,389,390,481,482,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,520,558,559,560,561,562,563,564,633,2174,2175,2180,2183,2188,2334,2335,2991,3025,3097,3130,3160,3193", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1751,5257,5329,6377,6442,8277,8346,15353,15423,15491,15563,15633,15694,15768,19090,19151,19212,19274,19338,19400,19461,19529,19629,19689,19755,19828,19897,19954,20006,24653,24725,24801,29519,29554,30020,30075,30138,30193,30251,30309,30370,30433,30490,30541,30591,30652,30709,30775,30809,30844,31634,34262,34329,34401,34470,34539,34613,34685,39763,140274,140391,140658,140951,141218,152791,152863,174471,176044,178850,180581,181581,182263", "endLines": "42,113,114,127,128,158,159,262,263,264,265,266,267,268,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,388,389,390,481,482,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,520,558,559,560,561,562,563,564,633,2174,2178,2180,2186,2188,2334,2335,2996,3034,3129,3150,3192,3198", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1806,5324,5412,6437,6503,8341,8404,15418,15486,15558,15628,15689,15763,15836,19146,19207,19269,19333,19395,19456,19524,19624,19684,19750,19823,19892,19949,20001,20063,24720,24796,24861,29549,29584,30070,30133,30188,30246,30304,30365,30428,30485,30536,30586,30647,30704,30770,30804,30839,30874,31699,34324,34396,34465,34534,34608,34680,34768,39829,140386,140587,140763,141147,141342,152858,152925,174669,176340,180576,181257,182258,182425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4d3998365c79e1c032d449e3242957ec\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "428", "startColumns": "4", "startOffsets": "27055", "endColumns": "49", "endOffsets": "27100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\00b00a57a1321b3abddaddf7bed980a7\\transformed\\navigation-runtime-2.8.3\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "484,2440,3454,3457", "startColumns": "4,4,4,4", "startOffsets": "29651,156453,191114,191229", "endLines": "484,2446,3456,3459", "endColumns": "52,24,24,24", "endOffsets": "29699,156752,191224,191339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\bdcbc52a7f4fc574f82c6a94b68ef448\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "511", "startColumns": "4", "startOffsets": "31068", "endColumns": "49", "endOffsets": "31113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "637,638", "startColumns": "4,4", "startOffsets": "40007,40063", "endColumns": "55,54", "endOffsets": "40058,40113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\3a07bceaa23ef51ac9866ac1768a7c38\\transformed\\androidsvg-aar-1.4\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "141", "endOffsets": "192"}, "to": {"startLines": "3604", "startColumns": "4", "startOffsets": "196497", "endColumns": "141", "endOffsets": "196634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1a24962cf41916db9572a74a797a0e06\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "510", "startColumns": "4", "startOffsets": "31014", "endColumns": "53", "endOffsets": "31063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\2beb69d6875208f2d04111a35001bba9\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "475", "startColumns": "4", "startOffsets": "29193", "endColumns": "65", "endOffsets": "29254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\3e8e26f56d8e20e616f6e0fb0878dba9\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "479,485", "startColumns": "4,4", "startOffsets": "29399,29704", "endColumns": "53,66", "endOffsets": "29448,29766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,427,429,430,476,478,513,565,566,567,568,569,622,623,624,625,626,627,629,630,631,632,634,635,636,1727,1896,1899", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24866,24925,24984,25044,25104,25164,25224,25284,25344,25404,25464,25524,25584,25643,25703,25763,25823,25883,25943,26003,26063,26123,26183,26243,26302,26362,26422,26481,26540,26599,26658,26717,26981,27105,27163,29259,29344,31182,34773,34838,34892,34958,35059,39204,39256,39316,39378,39432,39482,39589,39635,39681,39723,39834,39881,39917,109558,119082,119193", "endLines": "391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,427,429,430,476,478,513,565,566,567,568,569,622,623,624,625,626,627,629,630,631,632,634,635,636,1729,1898,1902", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "24920,24979,25039,25099,25159,25219,25279,25339,25399,25459,25519,25579,25638,25698,25758,25818,25878,25938,25998,26058,26118,26178,26238,26297,26357,26417,26476,26535,26594,26653,26712,26771,27050,27158,27213,29305,29394,31230,34833,34887,34953,35054,35112,39251,39311,39373,39427,39477,39531,39630,39676,39718,39758,39876,39912,40002,109665,119188,119383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9db5af2f2458547656275ea3d5385dab\\transformed\\media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "596,597,598,599,600,601,602,603,604", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "37249,37319,37381,37446,37510,37587,37652,37742,37826", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "37314,37376,37441,37505,37582,37647,37737,37821,37890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\02a2dc3fb6f2f628d8f50a1ef0801c78\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "556", "startColumns": "4", "startOffsets": "34137", "endColumns": "82", "endOffsets": "34215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e3284905063e776d62ae70595f5b34b4\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "474,489,512,3151,3156", "startColumns": "4,4,4,4,4", "startOffsets": "29136,29909,31118,181262,181432", "endLines": "474,489,512,3155,3159", "endColumns": "56,64,63,24,24", "endOffsets": "29188,29969,31177,181427,181576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\bd501f6f99f84825091a2bbfc5614134\\transformed\\media-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,5,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,288,410,476,598,659,725", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "139,345,471,593,654,720,787"}, "to": {"startLines": "160,483,2179,2181,2182,2187,2189", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8409,29589,140592,140768,140890,141152,141347", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "8493,29646,140653,140885,140946,141213,141409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5a79ec61513eb95e1266e907126a2152\\transformed\\media3-ui-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,39,40,41,42,43,44,45,46,47,48,49,54,61,62,63,64,65,66,67,72,73,74,75,76,77,78,79,80,81,82,83,84,85,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,235,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,291,295,299,303,307,311,315,316,322,333,337,341,345,349,353,357,361,365,369,373,377,390,395,400,405,418,426,436,440,444,448,451,467,493,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1143,1203,1262,1314,1364,1492,1557,1605,1654,1702,1759,1806,1861,1913,1967,2021,2075,2223,2461,2511,2560,2621,2681,2737,2797,2967,3027,3080,3137,3192,3248,3305,3354,3405,3460,3514,3573,3629,3684,3971,4036,4094,4143,4191,4242,4288,4345,4402,4464,4531,4603,4647,4704,4760,4823,4896,4966,5025,5082,5129,5184,5229,5278,5333,5387,5437,5488,5542,5601,5651,5709,5765,5818,5881,5946,6009,6061,6121,6185,6251,6309,6381,6442,6512,6582,6647,6712,6783,6878,6983,7086,7167,7250,7331,7420,7513,7606,7699,7784,7879,7972,8049,8141,8219,8299,8377,8463,8545,8638,8716,8807,8888,8977,9080,9181,9265,9361,9458,9553,9646,9738,9831,9924,10017,10100,10187,10282,10375,10477,10569,10650,10745,10838,10915,10959,11000,11045,11093,11137,11180,11229,11276,11320,11376,11429,11471,11518,11566,11626,11664,11714,11758,11797,11847,11899,11937,11984,12031,12072,12111,12149,12193,12241,12283,12321,12363,12417,12464,12501,12550,12592,12633,12674,12716,12759,12797,12833,12911,12989,13286,13556,13638,13720,13862,13940,14027,14112,14179,14242,14334,14426,14491,14554,14616,14687,14797,14908,15018,15085,15165,15236,15303,15388,15473,15536,15624,15688,15830,15930,15978,16121,16184,16246,16311,16382,16440,16498,16564,16616,16678,16754,16830,16884,16997,17276,17507,17717,17930,18140,18362,18578,18782,18820,19174,19961,20202,20442,20699,20952,21205,21440,21687,21926,22170,22391,22586,23258,23549,23845,24148,24814,25348,25822,26033,26233,26409,26517,27093,28038,29633", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,39,40,41,42,43,44,45,46,47,48,53,60,61,62,63,64,65,66,71,72,73,74,75,76,77,78,79,80,81,82,83,84,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,286,290,294,298,302,306,310,314,315,321,332,336,340,344,348,352,356,360,364,368,372,376,389,394,399,404,417,425,435,439,443,447,450,466,492,537,594", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1138,1198,1257,1309,1359,1487,1552,1600,1649,1697,1754,1801,1856,1908,1962,2016,2070,2218,2456,2506,2555,2616,2676,2732,2792,2962,3022,3075,3132,3187,3243,3300,3349,3400,3455,3509,3568,3624,3679,3966,4031,4089,4138,4186,4237,4283,4340,4397,4459,4526,4598,4642,4699,4755,4818,4891,4961,5020,5077,5124,5179,5224,5273,5328,5382,5432,5483,5537,5596,5646,5704,5760,5813,5876,5941,6004,6056,6116,6180,6246,6304,6376,6437,6507,6577,6642,6707,6778,6873,6978,7081,7162,7245,7326,7415,7508,7601,7694,7779,7874,7967,8044,8136,8214,8294,8372,8458,8540,8633,8711,8802,8883,8972,9075,9176,9260,9356,9453,9548,9641,9733,9826,9919,10012,10095,10182,10277,10370,10472,10564,10645,10740,10833,10910,10954,10995,11040,11088,11132,11175,11224,11271,11315,11371,11424,11466,11513,11561,11621,11659,11709,11753,11792,11842,11894,11932,11979,12026,12067,12106,12144,12188,12236,12278,12316,12358,12412,12459,12496,12545,12587,12628,12669,12711,12754,12792,12828,12906,12984,13281,13551,13633,13715,13857,13935,14022,14107,14174,14237,14329,14421,14486,14549,14611,14682,14792,14903,15013,15080,15160,15231,15298,15383,15468,15531,15619,15683,15825,15925,15973,16116,16179,16241,16306,16377,16435,16493,16559,16611,16673,16749,16825,16879,16992,17271,17502,17712,17925,18135,18357,18573,18777,18815,19169,19956,20197,20437,20694,20947,21200,21435,21682,21921,22165,22386,22581,23253,23544,23840,24143,24809,25343,25817,26028,26228,26404,26512,27088,28033,29628,31569"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,30,31,32,34,35,36,41,43,44,45,46,47,48,49,51,52,53,54,59,66,67,68,69,70,71,72,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,99,100,101,102,103,135,136,137,138,139,140,141,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,518,519,521,525,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,1730,1735,1739,1743,1747,1751,1755,1759,1763,1764,1770,1781,1785,1789,1793,1797,1801,1805,1809,1813,1817,1821,1825,1838,1843,1848,1853,1866,1874,1884,1888,1892,2984,3063,3199,3465,3510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1188,1243,1303,1407,1459,1509,1686,1811,1859,1908,1956,2013,2060,2115,2223,2277,2331,2385,2533,2771,2821,2870,2931,2991,3047,3107,3277,3337,3390,3447,3502,3558,3615,3664,3715,3770,3824,3883,3939,3994,4281,4387,4445,4494,4542,4593,6909,6966,7023,7085,7152,7224,7268,16014,16070,16133,16206,16276,16335,16392,16439,16494,16539,16588,16643,16697,16747,16798,16852,16911,16961,17019,17075,17128,17191,17256,17319,17371,17431,17495,17561,17619,17691,17752,17822,17892,17957,18022,20521,20616,20721,20824,20905,20988,21069,21158,21251,21344,21437,21522,21617,21710,21787,21879,21957,22037,22115,22201,22283,22376,22454,22545,22626,22715,22818,22919,23003,23099,23196,23291,23384,23476,23569,23662,23755,23838,23925,24020,24113,24215,24307,24388,24483,24576,27218,27262,27303,27348,27396,27440,27483,27532,27579,27623,27679,27732,27774,27821,27869,27929,27967,28017,28061,28100,28150,28202,28240,28287,28334,28375,28414,28452,28496,28544,28586,28624,28666,28720,28767,28804,28853,28895,28936,28977,29019,29062,29100,31478,31556,31704,32001,35117,35199,35281,35423,35501,35588,35673,35740,35803,35895,35987,36052,36115,36177,36248,36358,36469,36579,36646,36726,36797,36864,36949,37034,37097,37185,37895,38037,38137,38185,38328,38391,38453,38518,38589,38647,38705,38771,38823,38885,38961,39037,39091,109670,109949,110180,110390,110603,110813,111035,111251,111455,111493,111847,112634,112875,113115,113372,113625,113878,114113,114360,114599,114843,115064,115259,115931,116222,116518,116821,117487,118021,118495,118706,118906,174228,177702,182430,191526,193121", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,30,31,32,34,35,39,41,43,44,45,46,47,48,49,51,52,53,58,65,66,67,68,69,70,71,76,77,78,79,80,81,82,83,84,85,86,87,88,89,96,97,99,100,101,102,103,135,136,137,138,139,140,141,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,518,519,524,528,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,1734,1738,1742,1746,1750,1754,1758,1762,1763,1769,1780,1784,1788,1792,1796,1800,1804,1808,1812,1816,1820,1824,1837,1842,1847,1852,1865,1873,1883,1887,1891,1895,2986,3078,3224,3509,3566", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1238,1298,1357,1454,1504,1632,1746,1854,1903,1951,2008,2055,2110,2162,2272,2326,2380,2528,2766,2816,2865,2926,2986,3042,3102,3272,3332,3385,3442,3497,3553,3610,3659,3710,3765,3819,3878,3934,3989,4276,4341,4440,4489,4537,4588,4634,6961,7018,7080,7147,7219,7263,7320,16065,16128,16201,16271,16330,16387,16434,16489,16534,16583,16638,16692,16742,16793,16847,16906,16956,17014,17070,17123,17186,17251,17314,17366,17426,17490,17556,17614,17686,17747,17817,17887,17952,18017,18088,20611,20716,20819,20900,20983,21064,21153,21246,21339,21432,21517,21612,21705,21782,21874,21952,22032,22110,22196,22278,22371,22449,22540,22621,22710,22813,22914,22998,23094,23191,23286,23379,23471,23564,23657,23750,23833,23920,24015,24108,24210,24302,24383,24478,24571,24648,27257,27298,27343,27391,27435,27478,27527,27574,27618,27674,27727,27769,27816,27864,27924,27962,28012,28056,28095,28145,28197,28235,28282,28329,28370,28409,28447,28491,28539,28581,28619,28661,28715,28762,28799,28848,28890,28931,28972,29014,29057,29095,29131,31551,31629,31996,32266,35194,35276,35418,35496,35583,35668,35735,35798,35890,35982,36047,36110,36172,36243,36353,36464,36574,36641,36721,36792,36859,36944,37029,37092,37180,37244,38032,38132,38180,38323,38386,38448,38513,38584,38642,38700,38766,38818,38880,38956,39032,39086,39199,109944,110175,110385,110598,110808,111030,111246,111450,111488,111842,112629,112870,113110,113367,113620,113873,114108,114355,114594,114838,115059,115254,115926,116217,116513,116816,117482,118016,118490,118701,118901,119077,174331,178273,183370,193116,195057"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\mytv-android\\tv\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "19", "endColumns": "82", "endOffsets": "97"}, "to": {"startLines": "2226", "startColumns": "4", "startOffsets": "144050", "endColumns": "81", "endOffsets": "144127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\effa78477564d01358468880b73f82a7\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "50,306,307,308,316,317,318,480,3583", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2167,18093,18152,18200,18867,18942,19018,29453,195657", "endLines": "50,306,307,308,316,317,318,480,3603", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2218,18147,18195,18251,18937,19013,19085,29514,196492"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\mytv-android\\tv\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "41", "endOffsets": "54"}, "to": {"startLines": "557", "startColumns": "4", "startOffsets": "34220", "endColumns": "41", "endOffsets": "34257"}}]}]}