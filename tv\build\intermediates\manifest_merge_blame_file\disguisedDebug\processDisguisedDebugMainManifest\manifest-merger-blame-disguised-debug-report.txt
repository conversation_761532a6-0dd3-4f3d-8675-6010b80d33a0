1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.chinablue.tv"
4    android:versionCode="2"
5    android:versionName="3.3.9" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.touchscreen"
12-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:6:9-52
13        android:required="false" />
13-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:7:9-33
14    <uses-feature
14-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:8:5-10:36
15        android:name="android.software.leanback"
15-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:9:9-49
16        android:required="false" />
16-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:10:9-33
17
18    <queries>
18-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:12:5-14:15
19        <package android:name="com.google.android.webview" />
19-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:13:9-62
19-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:13:18-59
20    </queries>
21
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:16:5-67
22-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:16:22-64
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:17:5-81
23-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:17:22-78
24    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
24-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:18:5-83
24-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:18:22-80
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:19:5-80
25-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:19:22-77
26    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
26-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:20:5-22:40
26-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:21:9-66
27    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
27-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c1bb1cbb4c0c4dd816b59ba365baac29\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
27-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c1bb1cbb4c0c4dd816b59ba365baac29\transformed\media3-common-1.4.1\AndroidManifest.xml:22:22-76
28
29    <permission
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
30        android:name="com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.chinablue.tv.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
34
35    <application
35-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:24:5-81:19
36        android:name="top.yogiczy.mytv.tv.MyTVApplication"
36-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:25:9-40
37        package="is.xyz.mpv"
37-->[io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:8:9-29
38        android:allowBackup="true"
38-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:26:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:banner="@mipmap/tv_banner"
40-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:27:9-43
41        android:debuggable="true"
42        android:extractNativeLibs="true"
43        android:icon="@mipmap/ic_launcher"
43-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:28:9-43
44        android:label="@string/app_name"
44-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:29:9-41
45        android:networkSecurityConfig="@xml/network_security_config"
45-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:30:9-69
46        android:requestLegacyExternalStorage="true"
46-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:31:9-52
47        android:supportsRtl="true"
47-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:32:9-35
48        android:theme="@style/Theme.MyTV"
48-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:33:9-42
49        android:usesCleartextTraffic="true"
49-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:34:9-44
50        android:versionCode="1"
50-->[io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:9:9-32
51        android:versionName="1.0" >
51-->[io.github.abdallahmehiz:mpv-android-lib:0.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\dfda9b5f0a79a05127a5e51983ff9c49\transformed\mpv-android-lib-0.1.9\AndroidManifest.xml:10:9-34
52        <activity
52-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:36:9-50:20
53            android:name="top.yogiczy.mytv.tv.MainActivity"
53-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:37:13-41
54            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden"
54-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:38:13-119
55            android:exported="true"
55-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:39:13-36
56            android:resizeableActivity="true"
56-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:40:13-46
57            android:screenOrientation="sensorLandscape"
57-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:41:13-56
58            android:supportsPictureInPicture="true" >
58-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:42:13-52
59            <intent-filter>
59-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:44:13-49:29
60                <action android:name="android.intent.action.MAIN" />
60-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:45:17-69
60-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:45:25-66
61
62                <category android:name="android.intent.category.LAUNCHER" />
62-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:47:17-77
62-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:47:27-74
63                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
63-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:48:17-86
63-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:48:27-83
64            </intent-filter>
65        </activity>
66        <activity android:name="top.yogiczy.mytv.tv.CrashHandlerActivity" />
66-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:52:9-58
66-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:52:19-55
67
68        <receiver
68-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:54:9-63:20
69            android:name="top.yogiczy.mytv.tv.BootReceiver"
69-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:55:13-41
70            android:enabled="true"
70-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:56:13-35
71            android:exported="false"
71-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:57:13-37
72            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
72-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:58:13-75
73            <intent-filter>
73-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:59:13-62:29
74                <action android:name="android.intent.action.BOOT_COMPLETED" />
74-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:60:17-79
74-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:60:25-76
75
76                <category android:name="android.intent.category.DEFAULT" />
76-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:61:17-76
76-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:61:27-73
77            </intent-filter>
78        </receiver>
79
80        <service android:name="top.yogiczy.mytv.tv.HttpServerService" />
80-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:65:9-54
80-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:65:18-51
81
82        <provider
83            android:name="androidx.core.content.FileProvider"
83-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:68:13-62
84            android:authorities="com.chinablue.tv.FileProvider"
84-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:69:13-64
85            android:exported="false"
85-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:70:13-37
86            android:grantUriPermissions="true" >
86-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:71:13-47
87            <meta-data
87-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:72:13-74:54
88                android:name="android.support.FILE_PROVIDER_PATHS"
88-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:73:17-67
89                android:resource="@xml/file_paths" />
89-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:74:17-51
90        </provider>
91
92        <meta-data
92-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:77:9-79:37
93            android:name="io.sentry.auto-init"
93-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:78:13-47
94            android:value="false" />
94-->C:\Users\<USER>\StudioProjects\mytv-android\tv\src\main\AndroidManifest.xml:79:13-34
95
96        <!-- 'android:authorities' must be unique in the device, across all apps -->
97        <provider
97-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:12:9-15:40
98            android:name="io.sentry.android.core.SentryInitProvider"
98-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:13:13-69
99            android:authorities="com.chinablue.tv.SentryInitProvider"
99-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:14:13-70
100            android:exported="false" />
100-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:15:13-37
101        <provider
101-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:16:9-20:39
102            android:name="io.sentry.android.core.SentryPerformanceProvider"
102-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:17:13-76
103            android:authorities="com.chinablue.tv.SentryPerformanceProvider"
103-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:18:13-77
104            android:exported="false"
104-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:19:13-37
105            android:initOrder="200" />
105-->[io.sentry:sentry-android-core:7.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b522fd46c2f3e7cc994b0923a5769732\transformed\sentry-android-core-7.13.0\AndroidManifest.xml:20:13-36
106        <provider
106-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
107            android:name="androidx.startup.InitializationProvider"
107-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:25:13-67
108            android:authorities="com.chinablue.tv.androidx-startup"
108-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:26:13-68
109            android:exported="false" >
109-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:27:13-37
110            <meta-data
110-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
111-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
112                android:value="androidx.startup" />
112-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.emoji2.text.EmojiCompatInitializer"
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
115                android:value="androidx.startup" />
115-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
118                android:value="androidx.startup" />
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
119        </provider>
120
121        <activity
121-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:23:9-25:39
122            android:name="androidx.activity.ComponentActivity"
122-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:24:13-63
123            android:exported="true" />
123-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:25:13-36
124        <activity
124-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
125            android:name="androidx.compose.ui.tooling.PreviewActivity"
125-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
126            android:exported="true" />
126-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
127
128        <receiver
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
129            android:name="androidx.profileinstaller.ProfileInstallReceiver"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
130            android:directBootAware="false"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
131            android:enabled="true"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
132            android:exported="true"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
133            android:permission="android.permission.DUMP" >
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
134            <intent-filter>
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
135                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
136            </intent-filter>
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
138                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
141                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
142            </intent-filter>
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
144                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
145            </intent-filter>
146        </receiver>
147    </application>
148
149</manifest>
