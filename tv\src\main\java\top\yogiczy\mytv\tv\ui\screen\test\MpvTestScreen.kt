package top.yogiczy.mytv.tv.ui.screen.test

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.outlined.ArrowBack
import androidx.compose.material.icons.outlined.PlayArrow
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.tv.material3.Button
import androidx.tv.material3.Icon
import androidx.tv.material3.MaterialTheme
import androidx.tv.material3.Text
import androidx.compose.ui.viewinterop.AndroidView
import kotlinx.coroutines.CoroutineScope
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerDisplayMode
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerState
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.MpvVideoPlayer
import top.yogiczy.mytv.tv.ui.utils.Configs

@Composable
fun MpvTestScreen(
    modifier: Modifier = Modifier,
    onBackPressed: () -> Unit = {},
) {
    val context = LocalContext.current
    var url by remember { mutableStateOf("rtsp://***************:1554/iptv/import/Tvod/iptv/001/001/ch12122514263996485740.rsc/72156_Uni.sdp") }
    val coroutineScope: CoroutineScope = rememberCoroutineScope()
    val state: VideoPlayerState = remember {
        val player = MpvVideoPlayer(context, coroutineScope)
        VideoPlayerState(player) { VideoPlayerDisplayMode.FILL }
    }

    DisposableEffect(Unit) {
        state.initialize()
        onDispose { state.release() }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        Button(onClick = onBackPressed) {
            Icon(Icons.AutoMirrored.Outlined.ArrowBack, contentDescription = null)
            Text(text = "返回", style = MaterialTheme.typography.titleMedium, modifier = Modifier.padding(start = 8.dp))
        }

        Text(text = "MPV RTSP 测试 (UDP)", style = MaterialTheme.typography.headlineSmall)

        Text(text = "RTSP 地址", style = MaterialTheme.typography.titleSmall)
        BasicTextField(
            value = url,
            onValueChange = { url = it },
        )

        Button(onClick = {
            // Prepare MPV with UDP for the given RTSP url
            state.initialize()
            state.prepare(ChannelLine(url = url, httpUserAgent = Configs.videoPlayerUserAgent))
            state.play()
        }) {
            Icon(Icons.Outlined.PlayArrow, contentDescription = null)
            Text(text = "播放", modifier = Modifier.padding(start = 8.dp))
        }

        // Render player view
        val customView = state.instance.buildCustomView(context)
        if (customView != null) {
            AndroidView(
                modifier = Modifier.weight(1f),
                factory = { customView },
            )
        }
    }
}


